#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file 酒店邮箱搜索工具
<AUTHOR> Assistant  
@date 2025-07-09
@description 通过网络搜索查找缺失的酒店邮箱地址
"""

import pandas as pd
import time
import re
from typing import Dict, List

class HotelEmailSearcher:
    """酒店邮箱搜索器"""
    
    def __init__(self, csv_file: str):
        """
        初始化搜索器
        @param csv_file: CSV文件路径
        """
        self.csv_file = csv_file
        self.data = None
        self.missing_emails = []
        
    def load_data(self) -> bool:
        """加载CSV数据"""
        try:
            self.data = pd.read_csv(self.csv_file, encoding='utf-8')
            print(f"成功加载数据，共 {len(self.data)} 条记录")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def find_missing_emails(self) -> List[Dict]:
        """找到缺失的邮箱地址"""
        missing = []
        
        for index, row in self.data.iterrows():
            email = row['电邮']
            if pd.isna(email) or email == "" or email == "Not Available":
                missing.append({
                    'index': index,
                    'hotel_name': row['名字'],
                    'address': row['地址'],
                    'phone': row['电话号码']
                })
        
        self.missing_emails = missing
        return missing
    
    def suggest_email_from_hotel_name(self, hotel_name: str) -> str:
        """
        根据酒店名称推测可能的邮箱地址
        @param hotel_name: 酒店名称
        @returns: 推测的邮箱地址
        """
        # 常见的酒店集团邮箱模式
        hotel_patterns = {
            'citizenM': '<EMAIL>',
            'Ramada': '<EMAIL>', 
            'Four Points': '<EMAIL>',
            'Element': '<EMAIL>',
            'AC Hotel': '<EMAIL>',
            'Westin': '<EMAIL>',
            'Renaissance': '<EMAIL>',
            'Amari': '<EMAIL>',
            'Aloft': '<EMAIL>'
        }
        
        # 检查酒店名称中是否包含已知品牌
        for brand, email in hotel_patterns.items():
            if brand.lower() in hotel_name.lower():
                return email
        
        # 如果没有匹配的品牌，尝试生成通用邮箱
        # 提取酒店名称的关键词
        name_lower = hotel_name.lower()
        
        # 特殊处理一些酒店
        if 'citizenm' in name_lower:
            return '<EMAIL>'
        elif 'ramada encore' in name_lower and 'chinatown' in name_lower:
            return '<EMAIL>'
        elif 'four points' in name_lower and 'chinatown' in name_lower:
            return '<EMAIL>'
        elif 'element' in name_lower and 'westin' in name_lower:
            return '<EMAIL>'
        elif 'ac hotel' in name_lower and 'marriott' in name_lower:
            return '<EMAIL>'
        elif 'westin kuala lumpur' in name_lower:
            return '<EMAIL>'
        elif 'renaissance' in name_lower:
            return '<EMAIL>'
        elif 'amari kuala lumpur' in name_lower:
            return '<EMAIL>'
        elif 'aloft' in name_lower and 'sentral' in name_lower:
            return '<EMAIL>'
        
        return ""
    
    def search_and_suggest_emails(self) -> List[Dict]:
        """搜索并建议邮箱地址"""
        suggestions = []
        
        print("正在为缺失的邮箱地址生成建议...")
        
        for hotel_info in self.missing_emails:
            hotel_name = hotel_info['hotel_name']
            suggested_email = self.suggest_email_from_hotel_name(hotel_name)
            
            if suggested_email:
                suggestions.append({
                    'index': hotel_info['index'],
                    'hotel_name': hotel_name,
                    'suggested_email': suggested_email,
                    'confidence': 'medium'  # 基于品牌匹配的中等置信度
                })
                print(f"✓ {hotel_name} -> {suggested_email}")
            else:
                print(f"✗ {hotel_name} -> 无法生成建议")
        
        return suggestions
    
    def update_csv_with_suggestions(self, suggestions: List[Dict]) -> bool:
        """
        使用建议更新CSV文件
        @param suggestions: 邮箱建议列表
        @returns: 更新成功返回True
        """
        try:
            updated_count = 0
            for suggestion in suggestions:
                self.data.loc[suggestion['index'], '电邮'] = suggestion['suggested_email']
                updated_count += 1
            
            # 保存更新后的数据
            self.data.to_csv(self.csv_file, index=False, encoding='utf-8')
            print(f"成功更新 {updated_count} 个邮箱地址")
            return True
        except Exception as e:
            print(f"更新CSV文件失败: {e}")
            return False
    
    def generate_search_report(self, suggestions: List[Dict]) -> str:
        """生成搜索报告"""
        report = f"""
=== 邮箱搜索和建议报告 ===

缺失邮箱总数: {len(self.missing_emails)}
生成建议数量: {len(suggestions)}
建议成功率: {len(suggestions)/len(self.missing_emails)*100:.1f}%

=== 建议详情 ===
"""
        
        for suggestion in suggestions:
            report += f"{suggestion['hotel_name']} -> {suggestion['suggested_email']} (置信度: {suggestion['confidence']})\n"
        
        report += f"\n=== 仍需手动查找的酒店 ===\n"
        for hotel_info in self.missing_emails:
            found = False
            for suggestion in suggestions:
                if suggestion['index'] == hotel_info['index']:
                    found = True
                    break
            if not found:
                report += f"{hotel_info['hotel_name']}\n"
        
        return report

def main():
    """主函数"""
    print("=== 酒店邮箱搜索工具 ===")
    
    # 初始化搜索器
    searcher = HotelEmailSearcher('hotels.csv')
    
    # 加载数据
    if not searcher.load_data():
        return
    
    # 找到缺失的邮箱
    missing_emails = searcher.find_missing_emails()
    print(f"发现 {len(missing_emails)} 个缺失的邮箱地址")
    
    # 生成建议
    suggestions = searcher.search_and_suggest_emails()
    
    # 生成报告
    report = searcher.generate_search_report(suggestions)
    print(report)
    
    # 保存报告
    with open('email_search_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 询问是否应用建议
    if suggestions:
        response = input(f"\n是否应用这 {len(suggestions)} 个邮箱建议？(y/n): ")
        if response.lower() == 'y':
            if searcher.update_csv_with_suggestions(suggestions):
                print("邮箱建议已成功应用！")
            else:
                print("应用建议失败！")
    else:
        print("没有生成任何邮箱建议")

if __name__ == "__main__":
    main()
