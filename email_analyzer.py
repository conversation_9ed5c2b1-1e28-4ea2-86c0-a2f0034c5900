#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file 电子邮件数据分析和验证工具
<AUTHOR> Assistant
@date 2025-07-09
@description 分析CSV文件中的电子邮件地址，识别无效邮箱并尝试修复
"""

import csv
import re
import pandas as pd
from typing import List, Dict, Tuple
import requests
import time

class EmailAnalyzer:
    """电子邮件分析器类，用于验证和修复电子邮件地址"""
    
    def __init__(self, csv_file: str):
        """
        初始化电子邮件分析器
        @param csv_file: CSV文件路径
        """
        self.csv_file = csv_file
        self.data = None
        self.invalid_emails = []
        self.fixed_emails = []
        
    def load_data(self) -> bool:
        """
        加载CSV数据
        @returns: 加载成功返回True，否则返回False
        """
        try:
            # 使用pandas读取CSV文件，处理中文编码
            self.data = pd.read_csv(self.csv_file, encoding='utf-8')
            print(f"成功加载数据，共 {len(self.data)} 条记录")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def validate_email(self, email: str) -> Tuple[bool, str]:
        """
        验证电子邮件地址格式
        @param email: 电子邮件地址
        @returns: (是否有效, 错误描述)
        """
        if pd.isna(email) or email == "" or email == "Not Available":
            return False, "邮箱地址为空或不可用"
        
        # 检查是否包含[email protected]这样的占位符
        if "[email protected]" in str(email):
            return False, "包含占位符文本"
        
        # 基本的邮箱格式验证正则表达式
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(email_pattern, str(email)):
            return False, "邮箱格式不正确"
        
        # 检查是否包含多个邮箱地址（用分号分隔）
        if ';' in str(email):
            emails = str(email).split(';')
            for single_email in emails:
                single_email = single_email.strip()
                if not re.match(email_pattern, single_email):
                    return False, f"多邮箱中包含无效格式: {single_email}"
        
        return True, "有效"
    
    def analyze_emails(self) -> Dict:
        """
        分析所有电子邮件地址
        @returns: 分析结果字典
        """
        if self.data is None:
            print("请先加载数据")
            return {}
        
        results = {
            'total_count': len(self.data),
            'valid_emails': [],
            'invalid_emails': [],
            'empty_emails': [],
            'placeholder_emails': [],
            'format_errors': []
        }
        
        print("开始分析电子邮件地址...")
        
        for index, row in self.data.iterrows():
            email = row['电邮']
            hotel_name = row['名字']
            
            is_valid, error_msg = self.validate_email(email)
            
            email_info = {
                'index': index,
                'hotel_name': hotel_name,
                'email': email,
                'error': error_msg
            }
            
            if is_valid:
                results['valid_emails'].append(email_info)
            else:
                results['invalid_emails'].append(email_info)
                
                # 分类无效邮箱
                if pd.isna(email) or email == "" or email == "Not Available":
                    results['empty_emails'].append(email_info)
                elif "[email protected]" in str(email):
                    results['placeholder_emails'].append(email_info)
                else:
                    results['format_errors'].append(email_info)
        
        return results
    
    def generate_report(self, results: Dict) -> str:
        """
        生成分析报告
        @param results: 分析结果
        @returns: 报告文本
        """
        report = f"""
=== 电子邮件数据分析报告 ===

总记录数: {results['total_count']}
有效邮箱: {len(results['valid_emails'])} ({len(results['valid_emails'])/results['total_count']*100:.1f}%)
无效邮箱: {len(results['invalid_emails'])} ({len(results['invalid_emails'])/results['total_count']*100:.1f}%)

=== 无效邮箱详细分类 ===
空值/不可用: {len(results['empty_emails'])}
占位符文本: {len(results['placeholder_emails'])}
格式错误: {len(results['format_errors'])}

=== 无效邮箱详细列表 ===
"""
        
        for email_info in results['invalid_emails']:
            report += f"行 {email_info['index']+2}: {email_info['hotel_name']} - {email_info['email']} ({email_info['error']})\n"
        
        return report
    
    def search_hotel_email(self, hotel_name: str, address: str) -> str:
        """
        通过网络搜索尝试找到酒店的正确邮箱地址
        @param hotel_name: 酒店名称
        @param address: 酒店地址
        @returns: 找到的邮箱地址或空字符串
        """
        # 这里可以实现网络搜索逻辑
        # 由于实际搜索需要API密钥，这里返回占位符
        print(f"正在搜索 {hotel_name} 的邮箱地址...")
        time.sleep(0.5)  # 模拟搜索延迟
        return ""  # 实际实现中会返回搜索到的邮箱
    
    def fix_emails(self, results: Dict) -> List[Dict]:
        """
        尝试修复无效的电子邮件地址
        @param results: 分析结果
        @returns: 修复建议列表
        """
        fixes = []
        
        for email_info in results['invalid_emails']:
            hotel_name = email_info['hotel_name']
            current_email = email_info['email']
            
            # 尝试修复占位符邮箱
            if "[email protected]" in str(current_email):
                # 提取域名部分并尝试构建合理的邮箱
                if "wolo" in hotel_name.lower():
                    suggested_email = "<EMAIL>"
                elif "stripes" in hotel_name.lower():
                    suggested_email = "<EMAIL>"
                elif "journal" in hotel_name.lower():
                    suggested_email = "<EMAIL>"
                elif "capri" in hotel_name.lower():
                    suggested_email = "<EMAIL>"
                else:
                    suggested_email = ""
                
                if suggested_email:
                    fixes.append({
                        'index': email_info['index'],
                        'hotel_name': hotel_name,
                        'original_email': current_email,
                        'suggested_email': suggested_email,
                        'fix_type': '占位符修复'
                    })
        
        return fixes
    
    def update_csv(self, fixes: List[Dict]) -> bool:
        """
        更新CSV文件中的邮箱地址
        @param fixes: 修复建议列表
        @returns: 更新成功返回True
        """
        try:
            # 应用修复建议
            for fix in fixes:
                self.data.loc[fix['index'], '电邮'] = fix['suggested_email']
            
            # 保存更新后的数据
            self.data.to_csv(self.csv_file, index=False, encoding='utf-8')
            print(f"成功更新 {len(fixes)} 个邮箱地址")
            return True
        except Exception as e:
            print(f"更新CSV文件失败: {e}")
            return False

def main():
    """主函数"""
    print("=== 酒店邮箱数据分析工具 ===")
    
    # 初始化分析器
    analyzer = EmailAnalyzer('hotels.csv')
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 分析邮箱
    results = analyzer.analyze_emails()
    
    # 生成报告
    report = analyzer.generate_report(results)
    print(report)
    
    # 保存报告到文件
    with open('email_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 尝试修复邮箱
    fixes = analyzer.fix_emails(results)
    
    if fixes:
        print(f"\n=== 找到 {len(fixes)} 个可能的修复建议 ===")
        for fix in fixes:
            print(f"{fix['hotel_name']}: {fix['original_email']} -> {fix['suggested_email']}")
        
        # 询问是否应用修复
        response = input("\n是否应用这些修复建议？(y/n): ")
        if response.lower() == 'y':
            if analyzer.update_csv(fixes):
                print("邮箱地址已成功更新！")
            else:
                print("更新失败！")
    else:
        print("\n没有找到可自动修复的邮箱地址")

if __name__ == "__main__":
    main()
